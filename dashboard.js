// Declare pagination state variables at the global level
const paginationState = {
  currentPage: 1,
  itemsPerPage: 10,
  totalItems: 0,
};

// Custom confirmation dialog function
function showConfirmationDialog(title, message, onConfirm, onCancel, options) {
  // Get modal elements
  const modal = document.getElementById('confirmationModal');
  const titleElement = document.getElementById('confirmationTitle');
  const messageElement = document.getElementById('confirmationMessage');
  const confirmButton = document.getElementById('confirmOk');
  const cancelButton = document.getElementById('confirmCancel');
  const closeButton = document.getElementById('closeConfirmationModal');
  const optionsContainer = document.getElementById('confirmationOptions');
  const checkbox = document.getElementById('confirmationCheckbox');
  const checkboxLabel = document.getElementById('confirmationCheckboxLabel');

  // Set content
  titleElement.textContent = title;
  messageElement.textContent = message;

  // Handle checkbox options
  if (options && options.showCheckbox) {
    optionsContainer.style.display = 'block';
    checkbox.checked = options.checkboxDefaultChecked !== false;
    if (options.checkboxLabel) {
      checkboxLabel.textContent = options.checkboxLabel;
    }
  } else {
    optionsContainer.style.display = 'none';
  }

  // Show modal
  modal.style.display = 'block';

  // Remove any existing event listeners
  const newConfirmButton = confirmButton.cloneNode(true);
  confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

  const newCancelButton = cancelButton.cloneNode(true);
  cancelButton.parentNode.replaceChild(newCancelButton, cancelButton);

  const newCloseButton = closeButton.cloneNode(true);
  closeButton.parentNode.replaceChild(newCloseButton, closeButton);

  // Add event listeners
  newConfirmButton.addEventListener('click', function () {
    modal.style.display = 'none';
    if (typeof onConfirm === 'function') {
      // Pass checkbox state if it's visible
      if (options && options.showCheckbox) {
        onConfirm(checkbox.checked);
      } else {
        onConfirm();
      }
    }
  });

  function handleCancel() {
    modal.style.display = 'none';
    if (typeof onCancel === 'function') {
      onCancel();
    }
  }

  newCancelButton.addEventListener('click', handleCancel);
  newCloseButton.addEventListener('click', handleCancel);

  // Close when clicking outside - use once option to prevent multiple listeners
  const outsideClickHandler = function (event) {
    if (event.target === modal) {
      handleCancel();
      // Remove the event listener after it's used
      window.removeEventListener('click', outsideClickHandler);
    }
  };

  // Add the event listener
  window.addEventListener('click', outsideClickHandler);
}

document.addEventListener('DOMContentLoaded', function () {
  const dashboardContainer = document.getElementById('dashboardContainer');
  const userEmail = document.getElementById('userEmail');
  const logoutButton = document.getElementById('logoutButton');

  // Check if user is logged in
  const token = localStorage.getItem('testgorilla_token');
  const tokenExpiryTime = localStorage.getItem('testgorilla_token_expiry');
  const userInfo = localStorage.getItem('testgorilla_user');

  if (!token || !tokenExpiryTime || new Date().getTime() > parseInt(tokenExpiryTime)) {
    // Not logged in or token expired - redirect to login page immediately
    window.location.href = 'login.html';
    return;
  } else {
    // User is logged in
    dashboardContainer.style.display = 'block';

    // Display user email
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo);
        userEmail.textContent = user.email || 'User';
      } catch (e) {
        userEmail.textContent = 'User';
      }
    } else {
      userEmail.textContent = 'User';
    }

    // Add debug message
    // console.log('About to load assessments with token:', token);

    // Load assessments
    loadAssessments();

    // Set up refresh assessments button
    document.getElementById('refreshAssessmentsButton').addEventListener('click', function () {
      // console.log('Refresh button clicked');
      loadAssessments();
    });
  }

  // Logout button
  logoutButton.addEventListener('click', function () {
    localStorage.removeItem('testgorilla_token');
    localStorage.removeItem('testgorilla_token_expiry');
    localStorage.removeItem('testgorilla_user');
    window.location.href = 'login.html';
  });

  // Removed go to login button as we now redirect automatically

  // Pagination state is now declared at the global level

  // Function to load assessments from the API
  function loadAssessments(page = 1) {
    // console.log('loadAssessments called with page:', page);

    const assessmentsLoading = document.getElementById('assessmentsLoading');
    const assessmentsError = document.getElementById('assessmentsError');
    const assessmentsContainer = document.getElementById('assessmentsContainer');
    const assessmentsList = document.getElementById('assessmentsList');
    const noAssessments = document.getElementById('noAssessments');
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    const currentPageSpan = document.getElementById('currentPage');
    const paginationStart = document.getElementById('paginationStart');
    const paginationEnd = document.getElementById('paginationEnd');
    const paginationTotal = document.getElementById('paginationTotal');

    // console.log('DOM elements retrieved');

    // Update current page
    paginationState.currentPage = page;

    // Calculate offset
    const offset = (paginationState.currentPage - 1) * paginationState.itemsPerPage;
    // console.log('Calculated offset:', offset, 'for page:', page, 'with itemsPerPage:', paginationState.itemsPerPage);

    // Show loading state
    assessmentsLoading.style.display = 'block';
    assessmentsError.style.display = 'none';
    assessmentsContainer.style.display = 'none';
    // console.log('Set loading state');

    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');
    // console.log('Token retrieved from localStorage:', token ? 'Token exists' : 'No token found');

    if (!token) {
      assessmentsError.textContent = 'Authentication token not found. Please log in again.';
      assessmentsError.style.display = 'block';
      assessmentsLoading.style.display = 'none';
      console.error('No token found, showing error');
      return;
    }

    const apiUrl = `/proxy/assessments?limit=${paginationState.itemsPerPage}&offset=${offset}`;
    // console.log('Making API request to:', apiUrl);

    // Make API request to get assessments with pagination
    fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        // console.log('Received response:', response.status, response.statusText);
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        // console.log('Response is OK, parsing JSON');
        return response.json();
      })
      .then(data => {
        // console.log('Received data:', data);

        // Hide loading state
        assessmentsLoading.style.display = 'none';
        // console.log('Hidden loading state');

        // Update total items
        paginationState.totalItems = data.count || 0;
        // console.log('Updated total items:', paginationState.totalItems);

        // Update pagination info
        const start = paginationState.totalItems === 0 ? 0 : offset + 1;
        const end = Math.min(offset + paginationState.itemsPerPage, paginationState.totalItems);
        paginationStart.textContent = start;
        paginationEnd.textContent = end;
        paginationTotal.textContent = paginationState.totalItems;
        currentPageSpan.textContent = `Page ${paginationState.currentPage}`;
        // console.log('Updated pagination info:', start, end, paginationState.totalItems, paginationState.currentPage);

        // Update pagination buttons
        prevPageBtn.disabled = paginationState.currentPage <= 1;
        nextPageBtn.disabled = end >= paginationState.totalItems;

        // Check if there are assessments
        if (data.results && data.results.length > 0) {
          // Clear previous assessments
          assessmentsList.innerHTML = '';

          // Add each assessment to the table
          data.results.forEach(assessment => {
            const row = document.createElement('tr');

            // Format date
            const createdDate = new Date(assessment.created);
            const formattedDate =
              createdDate.toLocaleDateString() +
              ' ' +
              createdDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Create status badge
            const statusBadge = document.createElement('span');
            statusBadge.className = `badge badge-${assessment.status}`;
            statusBadge.textContent =
              assessment.status.charAt(0).toUpperCase() + assessment.status.slice(1);

            // Format completion percentage
            const completionPercentage =
              assessment.finished_percentage !== null
                ? `${assessment.finished_percentage}%`
                : 'N/A';

            // Create row content
            row.innerHTML = `
                                <td>${assessment.name}</td>
                                <td>${statusBadge.outerHTML}</td>
                                <td>${formattedDate}</td>
                                <td>${assessment.candidates}</td>
                                <td>${assessment.finished}</td>
                                <td>${completionPercentage}</td>
                                <td>
                                    <button class="button assessment-action" data-assessment-id="${assessment.id}" data-assessment-name="${assessment.name}">View Candidates</button>
                                </td>
                            `;

            assessmentsList.appendChild(row);
          });

          // Show assessments container
          assessmentsContainer.style.display = 'block';
          noAssessments.style.display = 'none';
        } else {
          // No assessments found
          assessmentsContainer.style.display = 'block';
          noAssessments.style.display = 'block';
          assessmentsList.innerHTML = '';
        }
      })
      .catch(error => {
        console.error('Error fetching assessments:', error);
        assessmentsError.textContent = `Error loading assessments: ${error.message}`;
        assessmentsError.style.display = 'block';
        assessmentsLoading.style.display = 'none';
        // console.log('Showing error message and hiding loading state');

        // Try to get more information about the error
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
          console.error('Network error - check if the server is running');
          assessmentsError.textContent +=
            '\n\nNetwork error - check if the server is running and that you are connected to the internet.';
        }
      });
  }

  // Set up pagination buttons
  document.getElementById('prevPageBtn').addEventListener('click', function () {
    if (paginationState.currentPage > 1) {
      loadAssessments(paginationState.currentPage - 1);
    }
  });

  document.getElementById('nextPageBtn').addEventListener('click', function () {
    loadAssessments(paginationState.currentPage + 1);
  });

  // Candidate modal state
  const candidateState = {
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    currentAssessmentId: null,
    currentAssessmentName: null,
    filters: {},
    allFilteredCandidates: [], // Store all candidates matching current filters
    totalFilteredCount: 0, // Total count of filtered candidates
    sorting: {
      field: 'created', // Default sort field
      direction: 'desc', // Default sort direction: 'asc' or 'desc'
    },
    // Persistent selection across pages
    selectedCandidates: {
      ids: new Set(), // Set of selected candidate IDs
      count: 0, // Total count of selected candidates
    },
  };

  // Initialize pagination features after candidateState is defined
  setupItemsPerPageDropdown();
  updateSelectedCount();

  // Set up event delegation for View Candidates buttons and sortable headers
  document.addEventListener('click', function (event) {
    // Handle assessment action buttons
    if (event.target.classList.contains('assessment-action')) {
      const assessmentId = event.target.getAttribute('data-assessment-id');
      const assessmentName = event.target.getAttribute('data-assessment-name');
      openCandidateModal(assessmentId, assessmentName);
    }

    // Handle sortable headers
    if (event.target.classList.contains('sortable')) {
      const sortField = event.target.getAttribute('data-sort-field');
      handleSort(sortField, event.target);
    }
  });

  // Function to handle sorting
  function handleSort(field, headerElement) {
    // Get all sortable headers
    const sortableHeaders = document.querySelectorAll('.sortable');

    // Remove sorting classes from all headers
    sortableHeaders.forEach(header => {
      header.classList.remove('asc', 'desc');
    });

    // Toggle sort direction if clicking on the same field
    if (candidateState.sorting.field === field) {
      candidateState.sorting.direction =
        candidateState.sorting.direction === 'asc' ? 'desc' : 'asc';
    } else {
      // Default to descending for a new field
      candidateState.sorting.field = field;
      candidateState.sorting.direction = 'desc';
    }

    // Add appropriate class to the clicked header
    headerElement.classList.add(candidateState.sorting.direction);

    // Reload candidates with new sorting
    loadCandidates(candidateState.currentAssessmentId, 1);
  }

  // Close modal when clicking the close button
  document.getElementById('closeModal').addEventListener('click', function () {
    document.getElementById('candidateModal').style.display = 'none';
  });

  // Close modal when clicking outside the modal content
  window.addEventListener('click', function (event) {
    const modal = document.getElementById('candidateModal');
    if (event.target === modal) {
      modal.style.display = 'none';
    }
  });

  // Set up filter buttons
  const filterButtons = document.querySelectorAll('.filter-button');
  filterButtons.forEach(button => {
    button.addEventListener('click', function () {
      // Get filter type, key and value
      const filterType = this.getAttribute('data-filter-type');
      const filterKey = this.getAttribute('data-filter-key');
      const filterValue = this.getAttribute('data-filter-value');

      // Remove active class from buttons of the same type
      document.querySelectorAll(`.filter-button[data-filter-type="${filterType}"]`).forEach(btn => {
        btn.classList.remove('active');
      });

      // Add active class to clicked button
      this.classList.add('active');

      // Update filters in state
      if (filterKey) {
        // If this is a filter button with a key (not 'All')
        candidateState.filters[filterType] = {
          key: filterKey,
          value: filterValue,
        };
      } else {
        // If this is an 'All' button, remove the filter for this type
        delete candidateState.filters[filterType];
      }

      // console.log('Updated filters:', candidateState.filters);

      // Reset to first page and load candidates with filters
      loadCandidates(candidateState.currentAssessmentId, 1);
    });
  });

  // Set up candidate pagination buttons
  document.getElementById('candidatePrevPageBtn').addEventListener('click', function () {
    if (candidateState.currentPage > 1) {
      loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage - 1);
    }
  });

  document.getElementById('candidateNextPageBtn').addEventListener('click', function () {
    loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage + 1);
  });

  // Function to open candidate modal and load candidates
  function openCandidateModal(assessmentId, assessmentName) {
    // Set current assessment ID and name
    candidateState.currentAssessmentId = assessmentId;
    candidateState.currentAssessmentName = assessmentName;
    candidateState.currentPage = 1;
    candidateState.filters = {}; // Reset filters

    // Clear any previous selections
    clearAllSelections();

    // Reset sorting to default (created, descending)
    candidateState.sorting = {
      field: 'created',
      direction: 'desc',
    };

    // Update modal title
    document.getElementById('modalAssessmentName').textContent = assessmentName;

    // Reset filter buttons
    filterButtons.forEach(btn => btn.classList.remove('active'));
    document
      .querySelector('.filter-button[data-filter-type="status"][data-filter-key=""]')
      .classList.add('active');
    document
      .querySelector('.filter-button[data-filter-type="stage"][data-filter-key=""]')
      .classList.add('active');

    // Reset sorting UI
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
      header.classList.remove('asc', 'desc');
      if (header.getAttribute('data-sort-field') === 'created') {
        header.classList.add('desc');
      }
    });

    // Show modal
    document.getElementById('candidateModal').style.display = 'block';

    // Load candidates
    loadCandidates(assessmentId, 1);
  }

  // Set up items per page dropdown
  function setupItemsPerPageDropdown() {
    const itemsPerPageSelect = document.getElementById('itemsPerPageSelect');
    if (!itemsPerPageSelect) {
      return;
    }

    // Set initial value
    itemsPerPageSelect.value = candidateState.itemsPerPage;

    // Add event listener
    itemsPerPageSelect.addEventListener('change', function () {
      // Update items per page in state
      candidateState.itemsPerPage = parseInt(this.value, 10);

      // Reload candidates with new items per page (reset to page 1)
      loadCandidates(candidateState.currentAssessmentId, 1);
    });
  }

  // Function to load candidates for an assessment
  function loadCandidates(assessmentId, page = 1) {
    const candidateLoading = document.getElementById('candidateLoading');
    const candidateError = document.getElementById('candidateError');
    const candidateTableContainer = document.getElementById('candidateTableContainer');
    const candidateTableBody = document.getElementById('candidateTableBody');
    const noCandidates = document.getElementById('noCandidates');
    const prevPageBtn = document.getElementById('candidatePrevPageBtn');
    const nextPageBtn = document.getElementById('candidateNextPageBtn');
    const currentPageSpan = document.getElementById('candidateCurrentPage');
    const paginationStart = document.getElementById('candidatePaginationStart');
    const paginationEnd = document.getElementById('candidatePaginationEnd');
    const paginationTotal = document.getElementById('candidatePaginationTotal');
    const deleteAllButton = document.getElementById('deleteAllButton');
    const rejectAllButton = document.getElementById('rejectAllButton');
    const itemsPerPageSelect = document.getElementById('itemsPerPageSelect');

    // Set items per page dropdown to current value
    if (itemsPerPageSelect) {
      itemsPerPageSelect.value = candidateState.itemsPerPage;
    }

    // Update current page
    candidateState.currentPage = page;

    // Calculate offset
    const offset = (candidateState.currentPage - 1) * candidateState.itemsPerPage;

    // Show loading state
    candidateLoading.style.display = 'block';
    candidateError.style.display = 'none';
    candidateTableContainer.style.display = 'none';

    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');

    if (!token) {
      candidateError.textContent = 'Authentication token not found. Please log in again.';
      candidateError.style.display = 'block';
      candidateLoading.style.display = 'none';
      return;
    }

    // Construct the URL with filters
    const params = new URLSearchParams();

    // Add assessment ID
    params.append('assessment', assessmentId);

    // Add all active filters
    for (const filterType in candidateState.filters) {
      const filter = candidateState.filters[filterType];
      if (filter.key && filter.value) {
        params.append(filter.key, filter.value);
      }
    }

    // Add pagination parameters
    params.append('limit', candidateState.itemsPerPage);
    params.append('offset', offset);

    // Add sorting parameter
    const sortPrefix = candidateState.sorting.direction === 'asc' ? '' : '-';
    params.append('ordering', `${sortPrefix}${candidateState.sorting.field}`);

    const apiUrl = `/proxy/candidates?${params.toString()}`;
    // console.log('Requesting candidates with URL:', apiUrl);

    // Make API request to get candidates
    fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        // Hide loading state
        candidateLoading.style.display = 'none';

        // Update total items
        candidateState.totalItems = data.count || 0;
        candidateState.totalFilteredCount = data.count || 0;

        // Update Delete/Reject All buttons text
        if (candidateState.totalFilteredCount > 0) {
          deleteAllButton.textContent = `Delete All (${candidateState.totalFilteredCount})`;
          rejectAllButton.textContent = `Reject All (${candidateState.totalFilteredCount})`;
          deleteAllButton.disabled = false;
          rejectAllButton.disabled = false;
        } else {
          deleteAllButton.textContent = 'Delete All Filtered';
          rejectAllButton.textContent = 'Reject All Filtered';
          deleteAllButton.disabled = true;
          rejectAllButton.disabled = true;
        }

        // Update pagination info
        const start = candidateState.totalItems === 0 ? 0 : offset + 1;
        const end = Math.min(offset + candidateState.itemsPerPage, candidateState.totalItems);
        paginationStart.textContent = start;
        paginationEnd.textContent = end;
        paginationTotal.textContent = candidateState.totalItems;
        currentPageSpan.textContent = `Page ${candidateState.currentPage}`;

        // Update pagination buttons
        prevPageBtn.disabled = candidateState.currentPage <= 1;
        nextPageBtn.disabled = end >= candidateState.totalItems;

        // Check if there are candidates
        if (data.results && data.results.length > 0) {
          // Clear previous candidates
          candidateTableBody.innerHTML = '';

          // Add each candidate to the table
          data.results.forEach(candidate => {
            const row = document.createElement('tr');

            // Format date
            const createdDate = new Date(candidate.created);
            const formattedDate =
              createdDate.toLocaleDateString() +
              ' ' +
              createdDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Format status
            const statusClass = `candidate-status candidate-status-${candidate.status}`;
            const statusText = candidate.status.charAt(0).toUpperCase() + candidate.status.slice(1);

            // Format stage
            let stageText = 'Unknown';
            let stageClass = '';

            if (candidate.stage === 'EVA') {
              stageText = 'Evaluated';
              stageClass = 'candidate-stage candidate-stage-EVA';
            } else if (candidate.stage === 'IFI') {
              stageText = 'Invited for Interview';
              stageClass = 'candidate-stage candidate-stage-IFI';
            } else if (candidate.stage === 'REJ') {
              stageText = 'Rejected';
              stageClass = 'candidate-stage candidate-stage-REJ';
            } else if (candidate.stage === 'NYE') {
              stageText = 'Not Yet Evaluated';
              stageClass = 'candidate-stage candidate-stage-NYE';
            }

            // Format score
            const score = candidate.display_normalized_weighted_percentile_score !== null ? `${candidate.display_normalized_weighted_percentile_score}` : '-';

            // Check if this candidate is in our persistent selection
            const isSelected = candidateState.selectedCandidates.ids.has(candidate.id);

            // Create row content
            row.innerHTML = `
                                <td>
                                    <input type="checkbox" class="candidate-checkbox ${isSelected ? 'persistent-selected' : ''}"
                                           data-candidate-id="${candidate.id}"
                                           data-candidate-name="${candidate.full_name || candidate.email}"
                                           ${isSelected ? 'checked' : ''}>
                                </td>
                                <td>${candidate.full_name || 'N/A'}</td>
                                <td>${candidate.email}</td>
                                <td><span class="${statusClass}">${statusText}</span></td>
                                <td><span class="${stageClass}">${stageText}</span></td>
                                <td>${score}</td>
                                <td>${formattedDate}</td>
                                <td>
                                    <button class="reject-button" data-candidate-id="${candidate.id}" data-candidate-name="${candidate.full_name || candidate.email}">Reject</button>
                                    <button class="delete-button" data-candidate-id="${candidate.id}" data-candidate-name="${candidate.full_name || candidate.email}">Delete</button>
                                </td>
                            `;

            candidateTableBody.appendChild(row);
          });

          // Show candidates table
          candidateTableContainer.style.display = 'block';
          noCandidates.style.display = 'none';

          // Set up buttons and checkboxes
          // Use setTimeout to ensure the DOM is fully updated
          setTimeout(() => {
            setupDeleteButtons();
            setupRejectButtons();
            setupCheckboxesAndButtons();
          }, 0);
        } else {
          // No candidates found
          candidateTableContainer.style.display = 'block';
          noCandidates.style.display = 'block';
          candidateTableBody.innerHTML = '';
        }
      })
      .catch(error => {
        console.error('Error fetching candidates:', error);
        candidateError.textContent = `Error loading candidates: ${error.message}`;
        candidateError.style.display = 'block';
        candidateLoading.style.display = 'none';
      });
  }

  // Function to set up delete buttons
  function setupDeleteButtons() {
    const deleteButtons = document.querySelectorAll('.delete-button');
    deleteButtons.forEach(button => {
      button.addEventListener('click', function () {
        const candidateId = this.getAttribute('data-candidate-id');
        const candidateName = this.getAttribute('data-candidate-name');

        showConfirmationDialog(
          'Confirm Deletion',
          `Are you sure you want to delete candidate ${candidateName}?`,
          function () {
            deleteCandidate(candidateId, candidateName);
          }
        );
      });
    });
  }

  // Function to set up reject buttons
  function setupRejectButtons() {
    const rejectButtons = document.querySelectorAll('.reject-button');
    rejectButtons.forEach(button => {
      button.addEventListener('click', function () {
        const candidateId = this.getAttribute('data-candidate-id');
        const candidateName = this.getAttribute('data-candidate-name');

        showConfirmationDialog(
          'Confirm Rejection',
          `Are you sure you want to reject candidate ${candidateName}?`,
          function (sendEmail) {
            rejectCandidate(candidateId, candidateName, sendEmail);
          },
          null,
          {
            showCheckbox: true,
            checkboxDefaultChecked: true,
            checkboxLabel: 'Send rejection email',
          }
        );
      });
    });
  }

  // Function to set up checkboxes
  function setupCheckboxes() {
    const bulkDeleteButton = document.getElementById('bulkDeleteButton');
    const selectedCountSpan = document.getElementById('selectedCount');

    // Check if required elements exist
    if (!bulkDeleteButton || !selectedCountSpan) {
      // console.log('One or more required elements for checkbox setup not found');
      return;
    }

    // Initialize the selected count
    updateSelectedCount();
  }

  // Update button state based on selection - global function
  function updateSelectedCount() {
    const bulkDeleteButton = document.getElementById('bulkDeleteButton');
    const bulkRejectButton = document.getElementById('bulkRejectButton');
    const selectedCountInfo = document.getElementById('selectedCountInfo');
    const selectedCountBadge = document.getElementById('selectedCountBadge');

    // Check if buttons exist before updating them
    if ((!bulkDeleteButton && !bulkRejectButton) || !selectedCountInfo || !selectedCountBadge) {
      // console.log('Required elements not found, skipping update');
      return;
    }

    // Update the count display
    const totalSelected = candidateState.selectedCandidates.count;
    selectedCountBadge.textContent = totalSelected;

    // Show/hide the selection info
    selectedCountInfo.style.display = totalSelected > 0 ? 'flex' : 'none';

    // Enable/disable buttons based on selection
    if (bulkDeleteButton) {
      bulkDeleteButton.disabled = totalSelected === 0;
    }
    if (bulkRejectButton) {
      bulkRejectButton.disabled = totalSelected === 0;
    }
  }

  // Add or remove a candidate from the persistent selection
  function toggleCandidateSelection(candidateId, isSelected) {
    if (isSelected) {
      candidateState.selectedCandidates.ids.add(candidateId);
    } else {
      candidateState.selectedCandidates.ids.delete(candidateId);
    }

    // Update the count
    candidateState.selectedCandidates.count = candidateState.selectedCandidates.ids.size;

    // Update UI
    updateSelectedCount();
  }

  // Clear all selections
  function clearAllSelections() {
    candidateState.selectedCandidates.ids.clear();
    candidateState.selectedCandidates.count = 0;
    updateSelectedCount();
  }

  // Select all checkbox - use event delegation for better handling
  document.addEventListener('change', function (event) {
    // Check if the changed element is the select all checkbox
    if (event.target.id === 'selectAllCheckbox') {
      // Get the current checkboxes (they might have changed since setup)
      const currentCheckboxes = document.querySelectorAll(
        '.candidate-table tbody tr td input.candidate-checkbox'
      );
      // console.log('Select all checkbox clicked, checkboxes count:', currentCheckboxes.length);

      // Update all visible checkboxes
      currentCheckboxes.forEach(checkbox => {
        checkbox.checked = event.target.checked;

        // Get candidate ID
        const candidateId = checkbox.getAttribute('data-candidate-id');
        if (candidateId) {
          // Update persistent selection
          toggleCandidateSelection(candidateId, event.target.checked);
        }
      });
    }
  });

  // Individual checkboxes - use event delegation for better handling of dynamically added checkboxes
  document.addEventListener('change', function (event) {
    // Check if the changed element is a candidate checkbox (but not the select all checkbox)
    if (
      event.target.classList.contains('candidate-checkbox') &&
      event.target.id !== 'selectAllCheckbox'
    ) {
      const selectAllCheckbox = document.getElementById('selectAllCheckbox');
      if (selectAllCheckbox) {
        // Update select all checkbox
        const totalCheckboxes = document.querySelectorAll(
          '.candidate-table tbody tr td input.candidate-checkbox'
        ).length;
        const checkedCheckboxes = document.querySelectorAll(
          '.candidate-table tbody tr td input.candidate-checkbox:checked'
        ).length;
        // console.log('Individual checkbox clicked, total:', totalCheckboxes, 'checked:', checkedCheckboxes);

        const allChecked = totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes;
        selectAllCheckbox.checked = allChecked;
      }

      // Get candidate ID
      const candidateId = event.target.getAttribute('data-candidate-id');
      if (candidateId) {
        // Update persistent selection
        toggleCandidateSelection(candidateId, event.target.checked);
      }
    }
  });

  // Set up bulk delete button
  function setupBulkDeleteButton() {
    const bulkDeleteButton = document.getElementById('bulkDeleteButton');
    if (!bulkDeleteButton) {
      // console.log('Bulk delete button not found');
      return;
    }

    // Remove existing event listeners by cloning and replacing the button
    const newBulkDeleteButton = bulkDeleteButton.cloneNode(true);
    bulkDeleteButton.parentNode.replaceChild(newBulkDeleteButton, bulkDeleteButton);

    // Add event listener to the new button
    newBulkDeleteButton.addEventListener('click', function () {
      const selectedCount = candidateState.selectedCandidates.count;
      // console.log('Bulk delete button clicked, selected count:', selectedCount);

      if (selectedCount === 0) {
        return;
      }

      // Confirm deletion with custom dialog
      showConfirmationDialog(
        'Confirm Bulk Deletion',
        `Are you sure you want to delete the selected ${selectedCount} candidate(s)?`,
        function () {
          // Get candidate IDs from persistent selection
          const candidateIds = Array.from(candidateState.selectedCandidates.ids);

          // Delete candidates
          bulkDeleteCandidates(candidateIds);

          // Clear selections after deletion
          clearAllSelections();
        }
      );
    });
  }

  // Set up bulk reject button
  function setupBulkRejectButton() {
    const bulkRejectButton = document.getElementById('bulkRejectButton');
    if (!bulkRejectButton) {
      // console.log('Bulk reject button not found');
      return;
    }

    // Remove existing event listeners by cloning and replacing the button
    const newBulkRejectButton = bulkRejectButton.cloneNode(true);
    bulkRejectButton.parentNode.replaceChild(newBulkRejectButton, bulkRejectButton);

    // Add event listener to the new button
    newBulkRejectButton.addEventListener('click', function () {
      const selectedCount = candidateState.selectedCandidates.count;
      // console.log('Bulk reject button clicked, selected count:', selectedCount);

      if (selectedCount === 0) {
        return;
      }

      // Confirm rejection with custom dialog
      showConfirmationDialog(
        'Confirm Bulk Rejection',
        `Are you sure you want to reject the selected ${selectedCount} candidate(s)?`,
        function (sendEmails) {
          // Get candidate IDs from persistent selection
          const candidateIds = Array.from(candidateState.selectedCandidates.ids);

          // Reject candidates
          bulkRejectCandidates(candidateIds, sendEmails);

          // Clear selections after rejection
          clearAllSelections();
        },
        null,
        {
          showCheckbox: true,
          checkboxDefaultChecked: true,
          checkboxLabel: 'Send rejection emails',
        }
      );
    });
  }

  // Function to get all candidates matching current filters
  function getAllFilteredCandidates() {
    return new Promise((resolve, reject) => {
      // Get token from localStorage
      const token = localStorage.getItem('testgorilla_token');

      if (!token) {
        reject(new Error('Authentication token not found. Please log in again.'));
        return;
      }

      // Construct the URL with filters but no pagination
      const params = new URLSearchParams();

      // Add assessment ID
      params.append('assessment', candidateState.currentAssessmentId);

      // Add all active filters
      for (const filterType in candidateState.filters) {
        const filter = candidateState.filters[filterType];
        if (filter.key && filter.value) {
          params.append(filter.key, filter.value);
        }
      }

      // Set a large limit to get all candidates in one request
      params.append('limit', 1000);
      params.append('offset', 0);

      // Add sorting parameter
      const sortPrefix = candidateState.sorting.direction === 'asc' ? '' : '-';
      params.append('ordering', `${sortPrefix}${candidateState.sorting.field}`);

      const apiUrl = `/proxy/candidates?${params.toString()}`;

      // Make API request to get all candidates
      fetch(apiUrl, {
        method: 'GET',
        headers: {
          Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
          'Content-Type': 'application/json',
        },
      })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          // Store the candidates and resolve the promise
          resolve(data.results || []);
        })
        .catch(error => {
          console.error('Error fetching all candidates:', error);
          reject(error);
        });
    });
  }

  // Set up delete all button
  function setupDeleteAllButton() {
    const deleteAllButton = document.getElementById('deleteAllButton');
    if (!deleteAllButton) {
      return;
    }

    // Remove existing event listeners by cloning and replacing the button
    const newDeleteAllButton = deleteAllButton.cloneNode(true);
    deleteAllButton.parentNode.replaceChild(newDeleteAllButton, deleteAllButton);

    // Add event listener to the new button
    newDeleteAllButton.addEventListener('click', function () {
      if (candidateState.totalFilteredCount === 0) {
        alert('No candidates match the current filters.');
        return;
      }

      // Confirm deletion with custom dialog
      showConfirmationDialog(
        'Confirm Delete All',
        `Are you sure you want to delete ALL ${candidateState.totalFilteredCount} candidates matching the current filters? This action cannot be undone.`,
        function () {
          // Get all filtered candidates
          getAllFilteredCandidates()
            .then(candidates => {
              const candidateIds = candidates.map(candidate => candidate.id);
              bulkDeleteCandidates(candidateIds);
            })
            .catch(error => {
              alert(`Error getting candidates: ${error.message}`);
            });
        }
      );
    });
  }

  // Set up reject all button
  function setupRejectAllButton() {
    const rejectAllButton = document.getElementById('rejectAllButton');
    if (!rejectAllButton) {
      return;
    }

    // Remove existing event listeners by cloning and replacing the button
    const newRejectAllButton = rejectAllButton.cloneNode(true);
    rejectAllButton.parentNode.replaceChild(newRejectAllButton, rejectAllButton);

    // Add event listener to the new button
    newRejectAllButton.addEventListener('click', function () {
      if (candidateState.totalFilteredCount === 0) {
        alert('No candidates match the current filters.');
        return;
      }

      // Confirm rejection with custom dialog
      showConfirmationDialog(
        'Confirm Reject All',
        `Are you sure you want to reject ALL ${candidateState.totalFilteredCount} candidates matching the current filters?`,
        function (sendEmails) {
          // Get all filtered candidates
          getAllFilteredCandidates()
            .then(candidates => {
              const candidateIds = candidates.map(candidate => candidate.id);
              bulkRejectCandidates(candidateIds, sendEmails);
            })
            .catch(error => {
              alert(`Error getting candidates: ${error.message}`);
            });
        },
        null,
        {
          showCheckbox: true,
          checkboxDefaultChecked: true,
          checkboxLabel: 'Send rejection emails',
        }
      );
    });
  }

  // Call setup functions when setting up checkboxes
  function setupCheckboxesAndButtons() {
    setupCheckboxes();
    setupBulkDeleteButton();
    setupBulkRejectButton();
    setupDeleteAllButton();
    setupRejectAllButton();
  }

  // Function to bulk delete candidates
  async function bulkDeleteCandidates(candidateIds) {
    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');

    if (!token) {
      alert('Authentication token not found. Please log in again.');
      return;
    }

    // Show loading state
    const bulkDeleteButton = document.getElementById('bulkDeleteButton');
    const originalText = bulkDeleteButton.textContent;
    bulkDeleteButton.textContent = 'Deleting...';
    bulkDeleteButton.disabled = true;

    // Hide any existing progress bar
    progressBar.hide();

    // Track progress
    let successCount = 0;
    let errorCount = 0;

    // Define the delete function for a single candidate
    const deleteCandidate = async candidateId => {
      try {
        const response = await fetch(`/proxy/candidates/${candidateId}`, {
          method: 'DELETE',
          headers: {
            Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          successCount++;
          return { success: true, candidateId };
        } else {
          errorCount++;
          console.error(
            `Error deleting candidate ${candidateId}: ${response.status} ${response.statusText}`
          );
          return { success: false, candidateId, status: response.status };
        }
      } catch (error) {
        errorCount++;
        console.error(`Error deleting candidate ${candidateId}:`, error);
        return { success: false, candidateId, error: error.message };
      }
    };

    try {
      // Process deletions sequentially with delay and progress bar
      await processSequentially(candidateIds, deleteCandidate, 1000, {
        showProgress: true,
        initialStatus: 'Preparing to delete candidates...',
        processingStatus: 'Deleting candidates...',
        completeStatus: 'Deletion complete',
        hideAfterComplete: false,
      });

      // Finish the bulk delete operation
      finishBulkDelete(successCount, errorCount);
    } catch (error) {
      console.error('Error in bulk delete process:', error);
      finishBulkDelete(successCount, errorCount);
    }

    // Function to finish bulk delete
    function finishBulkDelete(successCount, errorCount) {
      // Check if button still exists (modal might be closed)
      if (bulkDeleteButton) {
        // Reset button
        bulkDeleteButton.textContent = originalText;
        bulkDeleteButton.disabled = false;
      }

      // Update progress bar with final status
      if (errorCount === 0) {
        progressBar.complete(`Successfully deleted ${successCount} candidate(s)`);
      } else {
        progressBar.complete(`Deleted ${successCount}, failed to delete ${errorCount}`);
      }

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      // Show result
      if (errorCount === 0) {
        alert(`Successfully deleted ${successCount} candidate(s).`);
      } else {
        alert(`Deleted ${successCount} candidate(s). Failed to delete ${errorCount} candidate(s).`);
      }

      // Clear selections after deletion
      clearAllSelections();

      // Check if the candidate modal is still open
      const candidateModal = document.getElementById('candidateModal');
      if (candidateModal && candidateModal.style.display === 'block') {
        // Reload candidates
        loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage);
      }

      // Reload assessments to update candidate count
      loadAssessments(paginationState.currentPage);
    }
  }

  // Function to bulk reject candidates
  async function bulkRejectCandidates(candidateIds, sendEmails) {
    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');

    if (!token) {
      alert('Authentication token not found. Please log in again.');
      return;
    }

    // Show loading state
    const bulkRejectButton = document.getElementById('bulkRejectButton');
    const originalText = bulkRejectButton.textContent;
    bulkRejectButton.textContent = sendEmails ? 'Rejecting & Sending Emails...' : 'Rejecting...';
    bulkRejectButton.disabled = true;

    // Hide any existing progress bar
    progressBar.hide();

    // Track progress
    let successCount = 0;
    let errorCount = 0;
    let emailSentCount = 0;
    let emailErrorCount = 0;

    try {
      // Define the reject function for a single candidate
      const rejectCandidate = async candidateId => {
        try {
          const response = await fetch(`/proxy/candidates/${candidateId}/change_stage`, {
            method: 'PATCH',
            headers: {
              Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ stage: 'REJ' }),
          });

          if (response.ok) {
            successCount++;
            return { candidateId, success: true };
          } else {
            errorCount++;
            console.error(`Error rejecting candidate ${candidateId}: ${response.status}`);
            return { candidateId, success: false, status: response.status };
          }
        } catch (error) {
          errorCount++;
          console.error(`Error rejecting candidate ${candidateId}:`, error);
          return { candidateId, success: false, error: error.message };
        }
      };

      // Process rejections sequentially with delay and progress bar
      const rejectionResults = await processSequentially(candidateIds, rejectCandidate, 1000, {
        showProgress: true,
        initialStatus: 'Preparing to reject candidates...',
        processingStatus: 'Rejecting candidates...',
        completeStatus: 'Rejection complete',
        hideAfterComplete: false,
      });

      // Filter successful rejections for potential email sending
      const successfulCandidateIds = rejectionResults
        .filter(result => result.success)
        .map(result => result.candidateId);

      // Process emails if needed
      let emailsProcessed = false;
      if (successfulCandidateIds.length > 0 && sendEmails) {
        // Define the email function for a single candidate
        const sendEmail = async candidateId => {
          const result = await sendRejectionEmail(candidateId, 'candidate', token);
          if (result.emailSent) {
            emailSentCount++;
          } else {
            emailErrorCount++;
          }
          return result;
        };

        // Update progress bar for email sending phase
        progressBar.complete('Rejection complete. Preparing to send emails...');
        await delay(1000); // Short delay to show the transition

        // Process emails sequentially with delay and progress bar
        await processSequentially(successfulCandidateIds, sendEmail, 1000, {
          showProgress: true,
          initialStatus: 'Preparing to send rejection emails...',
          processingStatus: 'Sending rejection emails...',
          completeStatus: 'Email sending complete',
          hideAfterComplete: false,
        });

        emailsProcessed = true;
      }

      // Prepare result message
      let message = '';
      if (errorCount === 0) {
        message = `Successfully rejected ${successCount} candidate(s).`;
      } else {
        message = `Rejected ${successCount} candidate(s). Failed to reject ${errorCount} candidate(s).`;
      }

      // Add email results if emails were processed
      if (emailsProcessed) {
        if (emailErrorCount === 0 && emailSentCount > 0) {
          message += ` Sent ${emailSentCount} rejection email(s).`;
        } else if (emailSentCount > 0) {
          message += ` Sent ${emailSentCount} rejection email(s). Failed to send ${emailErrorCount} email(s).`;
        } else {
          message += ` Failed to send any rejection emails.`;
        }
      }

      // Reset button state
      if (bulkRejectButton) {
        bulkRejectButton.textContent = originalText;
        bulkRejectButton.disabled = false;
      }

      // Show alert with the message
      alert(message);

      // Clear selections after rejection
      clearAllSelections();

      // Check if the candidate modal is still open
      const candidateModal = document.getElementById('candidateModal');
      if (candidateModal && candidateModal.style.display === 'block') {
        // Reload candidates
        loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage);
      }

      // Update progress bar with final status
      progressBar.complete(`Operation complete: ${message}`);

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);
    } catch (error) {
      console.error('Error in bulk reject process:', error);

      // Reset button state
      if (bulkRejectButton) {
        bulkRejectButton.textContent = originalText;
        bulkRejectButton.disabled = false;
      }

      // Update progress bar with error status
      progressBar.complete(`Error: ${error.message}`);

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      // Show error
      alert(`Error during bulk rejection: ${error.message}`);

      // Reload candidates
      loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage);
    }
  }

  // Function to reject a single candidate
  async function rejectCandidate(candidateId, candidateName, sendEmail) {
    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');

    if (!token) {
      alert('Authentication token not found. Please log in again.');
      return;
    }

    // Hide any existing progress bar
    progressBar.hide();

    // Show progress bar for single candidate rejection
    progressBar.init(sendEmail ? 2 : 1, 'Rejecting candidate...');

    try {
      // Make API request to reject candidate
      const response = await fetch(`/proxy/candidates/${candidateId}/change_stage`, {
        method: 'PATCH',
        headers: {
          Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stage: 'REJ' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Update progress
      progressBar.update(1, 'Candidate rejected successfully');

      // Process based on sendEmail parameter
      if (sendEmail) {
        // Update progress for email sending
        progressBar.update(1, 'Sending rejection email...');

        // Add a small delay before sending the email to avoid rate limiting
        await delay(1000);

        // Send rejection email
        const emailResult = await sendRejectionEmail(candidateId, candidateName, token);

        // Update progress
        if (emailResult && emailResult.emailSent) {
          progressBar.complete('Candidate rejected and email sent successfully');
        } else {
          progressBar.complete('Candidate rejected but failed to send email');
        }

        // Prepare message
        let message = `Candidate ${candidateName} rejected successfully.`;
        if (emailResult && emailResult.emailSent) {
          message += ' Rejection email sent.';
        } else {
          message += ' Failed to send rejection email.';
        }

        // Show success message
        alert(message);
      } else {
        // Complete progress
        progressBar.complete('Candidate rejected successfully');

        // Don't send email
        alert(`Candidate ${candidateName} rejected successfully.`);
      }

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      // Reload candidates
      loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage);
    } catch (error) {
      console.error('Error rejecting candidate:', error);

      // Update progress bar with error
      progressBar.complete(`Error: ${error.message}`);

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      alert(`Error rejecting candidate: ${error.message}`);
    }
  }

  // Utility function to add delay between API calls
  function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Progress bar functions
  const progressBar = {
    container: document.getElementById('progressContainer'),
    bar: document.getElementById('progressBar'),
    status: document.getElementById('progressStatus'),
    count: document.getElementById('progressCount'),

    // Initialize the progress bar
    init: function (totalItems, statusText) {
      this.container.style.display = 'block';
      this.bar.style.width = '0%';
      this.bar.textContent = '0%';
      this.status.textContent = statusText || 'Processing...';
      this.count.textContent = `0 of ${totalItems}`;
      this.totalItems = totalItems;
      this.currentItem = 0;
    },

    // Update the progress bar
    update: function (currentItem, statusText) {
      if (statusText) {
        this.status.textContent = statusText;
      }

      this.currentItem = currentItem !== undefined ? currentItem : this.currentItem + 1;
      const percentage = Math.round((this.currentItem / this.totalItems) * 100);

      this.bar.style.width = `${percentage}%`;
      this.bar.textContent = `${percentage}%`;
      this.count.textContent = `${this.currentItem} of ${this.totalItems}`;
    },

    // Complete the progress bar
    complete: function (statusText) {
      this.bar.style.width = '100%';
      this.bar.textContent = '100%';
      this.status.textContent = statusText || 'Completed';
      this.count.textContent = `${this.totalItems} of ${this.totalItems}`;
    },

    // Hide the progress bar
    hide: function () {
      this.container.style.display = 'none';
    },
  };

  // Utility function for sequential processing with delay and progress updates
  async function processSequentially(items, processFn, delayMs = 1000, progressOptions = {}) {
    const results = [];
    const totalItems = items.length;

    // Initialize progress bar if options are provided
    if (progressOptions.showProgress) {
      progressBar.init(totalItems, progressOptions.initialStatus || 'Processing...');
    }

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      try {
        // Update progress before processing
        if (progressOptions.showProgress) {
          progressBar.update(i, progressOptions.processingStatus);
        }

        // Process the item
        const result = await processFn(item);
        results.push(result);

        // Update progress after processing
        if (progressOptions.showProgress) {
          progressBar.update(i + 1);
        }

        // Add delay before processing the next item
        if (i < items.length - 1) {
          await delay(delayMs);
        }
      } catch (error) {
        console.error('Error in sequential processing:', error);
        results.push({ error: true, message: error.message });

        // Update progress even on error
        if (progressOptions.showProgress) {
          progressBar.update(i + 1);
        }
      }
    }

    // Complete progress bar if options are provided
    if (progressOptions.showProgress) {
      progressBar.complete(progressOptions.completeStatus || 'Completed');

      // Hide progress bar after a delay if specified
      if (progressOptions.hideAfterComplete) {
        setTimeout(() => {
          progressBar.hide();
        }, progressOptions.hideDelay || 1000);
      }
    }

    return results;
  }

  // Function to send rejection email
  function sendRejectionEmail(candidateId, candidateName, token) {
    return fetch(`/proxy/candidates/${candidateId}/send-reminder`, {
      method: 'POST',
      headers: {
        Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}),
    })
      .then(response => {
        if (!response.ok) {
          console.error(`Error sending rejection email to ${candidateName}: ${response.status}`);
          return { emailSent: false, candidateId, status: response.status };
        }
        return { emailSent: true, candidateId };
      })
      .catch(error => {
        console.error(`Error sending rejection email to ${candidateName}:`, error);
        return { emailSent: false, candidateId, error: error.message };
      });
  }

  // Function to delete a single candidate
  async function deleteCandidate(candidateId, candidateName) {
    // Get token from localStorage
    const token = localStorage.getItem('testgorilla_token');

    if (!token) {
      alert('Authentication token not found. Please log in again.');
      return;
    }

    // Hide any existing progress bar
    progressBar.hide();

    // Show progress bar for single candidate deletion
    progressBar.init(1, 'Deleting candidate...');

    try {
      // Make API request to delete candidate
      const response = await fetch(`/proxy/candidates/${candidateId}`, {
        method: 'DELETE',
        headers: {
          Authorization: token.startsWith('Token ') ? token : `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Update progress
      progressBar.complete('Candidate deleted successfully');

      // Show success message
      alert(`Candidate ${candidateName} deleted successfully.`);

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      // Reload candidates after successful deletion
      loadCandidates(candidateState.currentAssessmentId, candidateState.currentPage);

      // Reload assessments to update candidate count
      loadAssessments(paginationState.currentPage);
    } catch (error) {
      console.error('Error deleting candidate:', error);

      // Update progress bar with error
      progressBar.complete(`Error: ${error.message}`);

      // Hide progress bar after a delay
      setTimeout(() => {
        progressBar.hide();
      }, 3000);

      alert(`Error deleting candidate: ${error.message}`);
    }
  }
});
