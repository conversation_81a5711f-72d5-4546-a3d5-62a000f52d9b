const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const bodyParser = require('body-parser');

const app = express();
const PORT = 80;

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(bodyParser.json());

// Serve static files from the current directory
app.use(express.static('./'));

// Proxy endpoint for TestGorilla login
app.post('/proxy/login', async (req, res) => {
  try {
    // console.log('Received login request:', req.body);

    // Forward the request to TestGorilla API
    const response = await fetch('https://app.testgorilla.com/api/profiles/login/', {
      method: 'POST',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        'content-type': 'application/json',
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: 'https://app.testgorilla.com/login',
        'x-datadog-origin': 'rum',
        'x-datadog-parent-id': '9222365778757852126',
        'x-datadog-sampling-priority': '1',
        'x-datadog-trace-id': '789779355925683066',
        'x-request-id': '8c47cc21-0039-4a6f-a946-6ef7aaa023d6',
      },
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    // Log the response (for debugging)
    // console.log('Response from TestGorilla:', data);

    // Send the response back to the client
    res.json(data);
  } catch (error) {
    console.error('Error proxying request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Proxy endpoint for TestGorilla assessments
app.get('/proxy/assessments', async (req, res) => {
  try {
    // Get the token from the request headers
    let token = req.headers.authorization;

    // console.log('Raw authorization header:', token);

    if (!token) {
      console.error('No authorization token provided');
      return res.status(401).json({ error: 'No authorization token provided' });
    }

    // Ensure token has the 'Token ' prefix
    if (!token.startsWith('Token ')) {
      token = `Token ${token}`;
      // console.log('Added Token prefix to authorization header');
    }

    // Get pagination parameters from query string
    const limit = req.query.limit || 10;
    const offset = req.query.offset || 0;
    const status = req.query.status || 'active';
    const ordering = req.query.ordering || '-created';

    // console.log(`Received assessments request with token: ${token}, limit: ${limit}, offset: ${offset}`);

    // Build the URL with pagination parameters
    const apiUrl = `https://app.testgorilla.com/api/assessments/?status=${status}&ordering=${ordering}&limit=${limit}&offset=${offset}`;
    // console.log('Making request to TestGorilla API:', apiUrl);

    // Forward the request to TestGorilla API with pagination parameters
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        authorization: token,
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: 'https://app.testgorilla.com/customer/assessments',
        'x-request-id': 'acb29393-eae7-49cd-b863-fc2ba710f426',
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      console.error(`TestGorilla API returned error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error('Error response body:', errorText);
      return res.status(response.status).json({
        error: `TestGorilla API error: ${response.status} ${response.statusText}`,
        details: errorText,
      });
    }

    // Get the response data
    const data = await response.json();

    // Log the response (for debugging)
    // console.log('Response from TestGorilla assessments:', data);

    // Send the response back to the client
    res.json(data);
  } catch (error) {
    console.error('Error proxying assessments request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Proxy endpoint for TestGorilla candidates
app.get('/proxy/candidates', async (req, res) => {
  try {
    // Get the token from the request headers
    let token = req.headers.authorization;

    // console.log('Raw authorization header for candidates:', token);

    if (!token) {
      console.error('No authorization token provided for candidates request');
      return res.status(401).json({ error: 'No authorization token provided' });
    }

    // Ensure token has the 'Token ' prefix
    if (!token.startsWith('Token ')) {
      token = `Token ${token}`;
      // console.log('Added Token prefix to authorization header for candidates');
    }

    // Get assessment ID and other parameters from query string
    const assessmentId = req.query.assessment;
    const limit = req.query.limit || 10;
    const offset = req.query.offset || 0;

    if (!assessmentId) {
      return res.status(400).json({ error: 'Assessment ID is required' });
    }

    // Build the base URL
    let apiUrl = `https://app.testgorilla.com/api/assessments/candidature/?assessment=${assessmentId}`;

    // Add all other query parameters (status, stage__in, etc.)
    for (const [key, value] of Object.entries(req.query)) {
      if (key !== 'assessment' && key !== 'limit' && key !== 'offset') {
        apiUrl += `&${key}=${value}`;
      }
    }

    // Get ordering parameter from query string or default to created
    const ordering = req.query.ordering || 'created';

    // Add pagination and ordering parameters
    apiUrl += `&ordering=${ordering}&limit=${limit}&offset=${offset}`;

    // console.log(`Received candidates request with token: ${token}, assessmentId: ${assessmentId}, ordering: ${ordering}`);
    // console.log(`Full query parameters:`, req.query);
    // console.log(`Making request to TestGorilla API with URL: ${apiUrl}`);
    // console.log('Making request to TestGorilla API for candidates:', apiUrl);

    // Forward the request to TestGorilla API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        authorization: token,
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: `https://app.testgorilla.com/customer/assessments/${assessmentId}`,
        'x-request-id': 'acb29393-eae7-49cd-b863-fc2ba710f426',
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      console.error(
        `TestGorilla API returned error for candidates: ${response.status} ${response.statusText}`
      );
      const errorText = await response.text();
      console.error('Error response body:', errorText);
      return res.status(response.status).json({
        error: `TestGorilla API error: ${response.status} ${response.statusText}`,
        details: errorText,
      });
    }

    // Get the response data
    const data = await response.json();

    // Log the response (for debugging)
    // console.log('Response from TestGorilla candidates:', data);

    // Send the response back to the client
    res.json(data);
  } catch (error) {
    console.error('Error proxying candidates request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Proxy endpoint for deleting a candidate
app.delete('/proxy/candidates/:candidateId', async (req, res) => {
  try {
    console.log(`[INFO] Received request to delete candidate ${req.params.candidateId}`);

    // Get the token from the request headers
    let token = req.headers.authorization;

    if (!token) {
      console.error('[ERROR] No authorization token provided for delete candidate request');
      return res.status(401).json({ error: 'No authorization token provided' });
    }

    // Ensure token has the 'Token ' prefix
    if (!token.startsWith('Token ')) {
      console.log('[INFO] Adding Token prefix to authorization header for delete candidate request');
      token = `Token ${token}`;
    }

    // Get candidate ID from URL parameters
    const candidateId = req.params.candidateId;

    if (!candidateId) {
      console.error('[ERROR] Candidate ID is missing in delete candidate request');
      return res.status(400).json({ error: 'Candidate ID is required' });
    }

    // Build the URL
    const apiUrl = `https://app.testgorilla.com/api/assessments/candidature/${candidateId}/`;
    console.log(`[INFO] Making request to TestGorilla API to delete candidate: ${apiUrl}`);

    // Forward the request to TestGorilla API
    console.log(`[INFO] Sending DELETE request for candidate ${candidateId}`);
    const response = await fetch(apiUrl, {
      method: 'DELETE',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        authorization: token,
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: 'https://app.testgorilla.com/customer/assessments',
        'x-request-id': 'acb29393-eae7-49cd-b863-fc2ba710f426',
      },
    });

    // Check if the response is OK
    if (!response.ok) {
      console.error(
        `[ERROR] TestGorilla API returned error for delete candidate: ${response.status} ${response.statusText}`
      );
      const errorText = await response.text();
      console.error('[ERROR] Error response body:', errorText);
      return res.status(response.status).json({
        error: `TestGorilla API error: ${response.status} ${response.statusText}`,
        details: errorText,
      });
    }

    console.log(`[INFO] Successfully deleted candidate ${candidateId}`);

    // Send success response back to the client
    res.status(204).send();
  } catch (error) {
    console.error('[ERROR] Error proxying delete candidate request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Proxy endpoint for changing candidate stage (e.g., rejecting)
app.patch('/proxy/candidates/:candidateId/change_stage', async (req, res) => {
  try {
    console.log(`[INFO] Received request to change stage for candidate ${req.params.candidateId} to ${req.body.stage || 'unknown stage'}`);

    // Get the token from the request headers
    let token = req.headers.authorization;

    if (!token) {
      console.error('[ERROR] No authorization token provided for change stage request');
      return res.status(401).json({ error: 'No authorization token provided' });
    }

    // Ensure token has the 'Token ' prefix
    if (!token.startsWith('Token ')) {
      console.log('[INFO] Adding Token prefix to authorization header for change stage request');
      token = `Token ${token}`;
    }

    // Get candidate ID from URL parameters
    const candidateId = req.params.candidateId;

    if (!candidateId) {
      console.error('[ERROR] Candidate ID is missing in change stage request');
      return res.status(400).json({ error: 'Candidate ID is required' });
    }

    // Get stage from request body
    const { stage } = req.body;

    if (!stage) {
      console.error('[ERROR] Stage is missing in change stage request body');
      return res.status(400).json({ error: 'Stage is required in request body' });
    }

    // Build the URL
    const apiUrl = `https://app.testgorilla.com/api/assessments/candidature/${candidateId}/change_stage/`;
    console.log(`[INFO] Making request to TestGorilla API to change candidate stage: ${apiUrl}`);

    // Forward the request to TestGorilla API
    console.log(`[INFO] Sending PATCH request to change candidate ${candidateId} stage to ${stage}`);
    const response = await fetch(apiUrl, {
      method: 'PATCH',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        authorization: token,
        'content-type': 'application/json',
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: 'https://app.testgorilla.com/customer/assessments',
        'x-request-id': 'acb29393-eae7-49cd-b863-fc2ba710f426',
      },
      body: JSON.stringify({ stage }),
    });

    // Check if the response is OK
    if (!response.ok) {
      console.error(
        `[ERROR] TestGorilla API returned error for change stage: ${response.status} ${response.statusText}`
      );
      const errorText = await response.text();
      console.error('[ERROR] Error response body:', errorText);
      return res.status(response.status).json({
        error: `TestGorilla API error: ${response.status} ${response.statusText}`,
        details: errorText,
      });
    }

    // Get the response data
    const data = await response.json();
    console.log(`[INFO] Successfully changed stage for candidate ${candidateId} to ${stage}`);

    // Send response back to the client
    res.json(data);
  } catch (error) {
    console.error('[ERROR] Error proxying change stage request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Proxy endpoint for sending reminder/notification to a candidate
app.post('/proxy/candidates/:candidateId/send-reminder', async (req, res) => {
  try {
    console.log(`[INFO] Received request to send reminder/notification to candidate ${req.params.candidateId}`);

    // Get the token from the request headers
    let token = req.headers.authorization;

    if (!token) {
      console.error('[ERROR] No authorization token provided for send reminder request');
      return res.status(401).json({ error: 'No authorization token provided' });
    }

    // Ensure token has the 'Token ' prefix
    if (!token.startsWith('Token ')) {
      console.log('[INFO] Adding Token prefix to authorization header for send reminder request');
      token = `Token ${token}`;
    }

    // Get candidate ID from URL parameters
    const candidateId = req.params.candidateId;

    if (!candidateId) {
      console.error('[ERROR] Candidate ID is missing in send reminder request');
      return res.status(400).json({ error: 'Candidate ID is required' });
    }

    // Build the URL
    const apiUrl = `https://app.testgorilla.com/api/assessments/candidature/${candidateId}/send-reminder/`;
    console.log(`[INFO] Making request to TestGorilla API to send reminder: ${apiUrl}`);

    // Forward the request to TestGorilla API
    console.log(`[INFO] Sending POST request to send reminder to candidate ${candidateId}`);
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'en, en',
        authorization: token,
        'content-type': 'application/json',
        priority: 'u=1, i',
        'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        origin: 'https://app.testgorilla.com',
        host: 'app.testgorilla.com',
        referer: 'https://app.testgorilla.com/customer/assessments',
        'x-request-id': 'acb29393-eae7-49cd-b863-fc2ba710f426',
      },
      body: JSON.stringify({}),
    });

    // Check if the response is OK
    if (!response.ok) {
      console.error(
        `[ERROR] TestGorilla API returned error for send reminder: ${response.status} ${response.statusText}`
      );
      const errorText = await response.text();
      console.error('[ERROR] Error response body:', errorText);
      return res.status(response.status).json({
        error: `TestGorilla API error: ${response.status} ${response.statusText}`,
        details: errorText,
      });
    }

    console.log(`[INFO] Successfully sent reminder to candidate ${candidateId}`);

    // Send response back to the client
    res.json({ emailSent: true });
  } catch (error) {
    console.error('[ERROR] Error proxying send reminder request:', error);
    res.status(500).json({ error: error.message });
  }
});

// Redirect root to login page
app.get('/', (_, res) => {
  res.redirect('/login.html');
});

// Start the server
app.listen(PORT, () => {
  console.log(`[INFO] ========================================`);
  console.log(`[INFO] TestGorilla Proxy Server`);
  console.log(`[INFO] Server running at https://proxy.testgorilla.coolify.orderstack.dev/`);
  console.log(`[INFO] Port: ${PORT}`);
  console.log(`[INFO] ========================================`);
});
