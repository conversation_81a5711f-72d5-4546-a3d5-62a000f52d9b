name: Docker Build and Push

on:
  push:
    branches:
      - main

jobs:
  check-commit-message:
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.check-commit.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 2

      - name: Check commit message
        id: check-commit
        run: |
          COMMIT_MSG=$(git log -1 --pretty=format:"%s")
          echo "Commit message: $COMMIT_MSG"
          if [[ "$COMMIT_MSG" == *"deploy_image"* ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "Commit message contains 'deploy_image', will proceed with deployment"
          else
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            echo "Commit message does not contain 'deploy_image', skipping deployment"
          fi

  build-and-push:
    needs: check-commit-message
    if: needs.check-commit-message.outputs.should-deploy == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/testgorilla-proxy:latest
          cache-from: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/testgorilla-proxy:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKERHUB_USERNAME }}/testgorilla-proxy:buildcache,mode=max
  deploy:
    name: restart docker container
    runs-on: ubuntu-latest
    needs: build-and-push
    steps:
      - name: trigger coolify webhook
        run: |
          curl -X GET \
          -H "Authorization: Bearer ${{ secrets.COOLIFY_API_KEY }}" \
          "https://coolify.orderstack.dev/api/v1/deploy?uuid=no0ggo4ogsw0s0wkg448wsg4&force=false"
