<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TestGorilla Assessment Management</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --primary-light: #eff6ff;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Animated background pattern */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            background-color: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 3rem;
            width: 100%;
            max-width: 420px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .logo::before {
            content: "🎯";
            font-size: 2rem;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        h1 {
            text-align: center;
            color: var(--text-primary);
            margin-bottom: 2rem;
            font-weight: 600;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
            font-size: 0.875rem;
        }

        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.2s ease;
            background-color: var(--surface-color);
            color: var(--text-primary);
        }

        input[type="email"]:focus,
        input[type="password"]:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            transform: translateY(-1px);
        }

        button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: 0.875rem 1rem;
            width: 100%;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: var(--secondary-color);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        button:disabled::before {
            display: none;
        }

        .error-message {
            color: var(--danger-color);
            margin-top: 1.5rem;
            text-align: center;
            font-size: 0.875rem;
            padding: 1rem;
            background-color: #fee2e2;
            border-radius: var(--radius-md);
            border: 1px solid #fecaca;
            animation: shake 0.5s ease-in-out;
        }

        .success-message {
            color: var(--success-color);
            margin-top: 1.5rem;
            text-align: center;
            font-size: 0.875rem;
            padding: 1rem;
            background-color: #d1fae5;
            border-radius: var(--radius-md);
            border: 1px solid #a7f3d0;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: inline-block;
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .security-notice {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--border-color);
        }

        .security-notice p {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.5;
        }

        .security-icon {
            font-size: 0.875rem;
            margin-right: 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .container {
                margin: 1rem;
                padding: 2rem;
                max-width: none;
            }

            .logo {
                font-size: 1.5rem;
            }

            h1 {
                font-size: 1.25rem;
            }
        }

        /* Focus states for accessibility */
        input:focus,
        button:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Additional professional touches */
        .input-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .form-group:first-of-type .input-icon::before {
            content: "📧";
        }

        .form-group:last-of-type .input-icon::before {
            content: "🔒";
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="logo-container">
            <div class="logo">TestGorilla</div>
            <div class="logo-subtitle">Assessment Management System</div>
        </div>
        <h1>Welcome Back</h1>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email">
                <div class="input-icon"></div>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required placeholder="Enter your password">
                <div class="input-icon"></div>
            </div>

            <button type="submit" id="loginButton">
                <span id="loadingIndicator" class="loading hidden"></span>
                <span id="buttonText">Sign In</span>
            </button>
        </form>

        <div id="errorMessage" class="error-message hidden"></div>
        <div id="successMessage" class="success-message hidden"></div>

        <div class="security-notice">
            <p>
                <span class="security-icon">🔐</span>
                Your connection is secure and encrypted. We protect your privacy and data.
            </p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Check if user is already logged in
            const token = localStorage.getItem('testgorilla_token');
            const tokenExpiry = localStorage.getItem('testgorilla_token_expiry');

            if (token && tokenExpiry && new Date().getTime() < parseInt(tokenExpiry)) {
                // Token exists and is not expired, redirect to dashboard
                window.location.href = 'dashboard.html';
                return;
            }

            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const buttonText = document.getElementById('buttonText');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            loginForm.addEventListener('submit', async function (e) {
                e.preventDefault();

                // Get form values
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;

                // Show loading state
                loginButton.disabled = true;
                loadingIndicator.classList.remove('hidden');
                buttonText.textContent = 'Signing in...';
                errorMessage.classList.add('hidden');
                successMessage.classList.add('hidden');

                try {
                    // Send login request to our proxy server
                    const response = await fetch('/proxy/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            username: email,
                            password: password
                        })
                    });

                    const data = await response.json();

                    if (response.ok && data.token) {
                        // Login successful
                        // Store token in localStorage
                        localStorage.setItem('testgorilla_token', data.token);

                        // Store user info if available
                        if (data.user) {
                            localStorage.setItem('testgorilla_user', JSON.stringify(data.user));
                        }

                        // Set token expiry (24 hours from now)
                        const expiryTime = new Date().getTime() + (24 * 60 * 60 * 1000);
                        localStorage.setItem('testgorilla_token_expiry', expiryTime.toString());

                        // Show success message
                        successMessage.textContent = 'Login successful! Redirecting to dashboard...';
                        successMessage.classList.remove('hidden');

                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    } else {
                        // Login failed
                        errorMessage.textContent = data.detail || data.error || 'Invalid email or password. Please try again.';
                        errorMessage.classList.remove('hidden');

                        // Reset button
                        loginButton.disabled = false;
                        loadingIndicator.classList.add('hidden');
                        buttonText.textContent = 'Sign In';
                    }
                } catch (error) {
                    // Error occurred
                    errorMessage.textContent = 'Unable to connect to the server. Please check your connection and try again.';
                    errorMessage.classList.remove('hidden');
                    console.error('Login error:', error);

                    // Reset button
                    loginButton.disabled = false;
                    loadingIndicator.classList.add('hidden');
                    buttonText.textContent = 'Sign In';
                }
            });

            // Add input animation effects
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>

</html>
