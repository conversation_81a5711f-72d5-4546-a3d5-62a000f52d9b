<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestGorilla Dashboard</title>
    <link rel="stylesheet" href="dashboard.css">
</head>

<body>
    <div class="header">
        <div class="logo">TestGorilla</div>
        <div class="user-menu">
            <div class="user-info" id="userEmail">Loading...</div>
            <button class="logout-button" id="logoutButton">Logout</button>
        </div>
    </div>

    <div class="container" id="dashboardContainer">
        <div class="dashboard-header">
            <h1>Dashboard</h1>
            <div class="subtitle">Welcome to your TestGorilla dashboard</div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2 class="card-title">Your Assessments</h2>
                <button class="button" id="refreshAssessmentsButton">Refresh Assessments</button>
            </div>

            <div id="assessmentsLoading" class="loading-container">
                <div class="loading-spinner"></div>
                <p>Loading assessments...</p>
            </div>

            <div id="assessmentsError" class="error-message" style="display: none;"></div>

            <div id="assessmentsContainer" style="display: none;">
                <table class="assessments-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Candidates</th>
                            <th>Finished</th>
                            <th>Completion %</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="assessmentsList">
                        <!-- Assessment items will be added here dynamically -->
                    </tbody>
                </table>

                <div id="noAssessments" style="display: none;" class="empty-state">
                    <p>No assessments found.</p>
                </div>

                <div class="pagination-container">
                    <div class="pagination-info">
                        <div class="pagination-text">
                            Showing <span id="paginationStart">0</span> to <span id="paginationEnd">0</span> of <span
                                id="paginationTotal">0</span> assessments
                        </div>
                    </div>
                    <div class="pagination-controls">
                        <button id="prevPageBtn" class="button secondary pagination-button" disabled>Previous</button>
                        <span id="currentPage" class="pagination-page">Page 1</span>
                        <button id="nextPageBtn" class="button secondary pagination-button">Next</button>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content confirmation-modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="confirmationTitle">Confirm Action</h2>
                <button class="close-button" id="closeConfirmationModal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to perform this action?</p>
                <div id="confirmationOptions" style="display: none; margin-top: 15px;">
                    <label class="checkbox-container">
                        <input type="checkbox" id="confirmationCheckbox" checked>
                        <span id="confirmationCheckboxLabel">Send rejection email</span>
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button id="confirmCancel" class="button secondary">Cancel</button>
                <button id="confirmOk" class="button">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Candidate Details Modal -->
    <div id="candidateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Candidates for <span id="modalAssessmentName"></span></h2>
                <button class="close-button" id="closeModal">&times;</button>
            </div>

            <div class="filter-container">
                <div class="filter-group">
                    <h4 class="filter-heading">Status:</h4>
                    <button class="filter-button active" data-filter-type="status" data-filter-key="">All</button>
                    <button class="filter-button" data-filter-type="status" data-filter-key="status"
                        data-filter-value="completed">Completed</button>
                    <button class="filter-button" data-filter-type="status" data-filter-key="status"
                        data-filter-value="started">Started</button>
                    <button class="filter-button" data-filter-type="status" data-filter-key="status"
                        data-filter-value="invited">Invited</button>
                    <button class="filter-button" data-filter-type="status" data-filter-key="status"
                        data-filter-value="expired">Expired</button>
                </div>

                <div class="filter-group">
                    <h4 class="filter-heading">Stage:</h4>
                    <button class="filter-button active" data-filter-type="stage" data-filter-key="">All</button>
                    <button class="filter-button" data-filter-type="stage" data-filter-key="stage__in"
                        data-filter-value="EVA">Evaluated</button>
                    <button class="filter-button" data-filter-type="stage" data-filter-key="stage__in"
                        data-filter-value="IFI">Invited for Interview</button>
                    <button class="filter-button" data-filter-type="stage" data-filter-key="stage__in"
                        data-filter-value="REJ">Rejected</button>
                    <button class="filter-button" data-filter-type="stage" data-filter-key="stage__in"
                        data-filter-value="NYE">Not Yet Evaluated</button>
                </div>
            </div>

            <div id="candidateLoading" class="candidate-loading">
                <div class="loading-spinner"></div>
                <p>Loading candidates...</p>
            </div>

            <div id="candidateError" class="candidate-error" style="display: none;"></div>

            <div id="candidateTableContainer" style="display: none;">
                <div class="bulk-actions">
                    <div class="bulk-selected-actions">
                        <button id="bulkRejectButton" class="button secondary" disabled>Reject Selected</button>
                        <button id="bulkDeleteButton" class="button danger" disabled>Delete Selected</button>
                    </div>
                    <div class="bulk-all-actions">
                        <button id="rejectAllButton" class="button secondary">Reject All Filtered</button>
                        <button id="deleteAllButton" class="button danger">Delete All Filtered</button>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div id="progressContainer" class="progress-container">
                    <div id="progressBar" class="progress-bar">0%</div>
                    <div class="progress-info">
                        <div id="progressStatus" class="progress-status">Processing...</div>
                        <div id="progressCount" class="progress-count">0 of 0</div>
                    </div>
                </div>
                <table class="candidate-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllCheckbox" class="candidate-checkbox">
                            </th>
                            <th class="sortable" data-sort-field="_full_name">Name</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Stage</th>
                            <th class="sortable" data-sort-field="normalized_percentile_score">Overall Percentile</th>
                            <th class="sortable desc" data-sort-field="created">Invited on</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="candidateTableBody">
                        <!-- Candidate rows will be added here -->
                    </tbody>
                </table>

                <div id="noCandidates" class="no-candidates" style="display: none;">
                    <p>No candidates found for this assessment.</p>
                </div>

                <div class="pagination-container">
                    <div class="pagination-info">
                        <div class="pagination-text">
                            Showing <span id="candidatePaginationStart">0</span> to <span
                                id="candidatePaginationEnd">0</span> of <span id="candidatePaginationTotal">0</span>
                            candidates
                        </div>
                        <div class="selected-count-info" id="selectedCountInfo" style="display: none;">
                            <span id="selectedCountBadge" class="selected-count-badge">0</span> candidates selected
                            across all pages
                        </div>
                    </div>
                    <div class="pagination-controls">
                        <div class="items-per-page-container">
                            <label for="itemsPerPageSelect">Items per page:</label>
                            <select id="itemsPerPageSelect" class="items-per-page-select">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="page-navigation">
                            <button id="candidatePrevPageBtn" class="button secondary pagination-button"
                                disabled>Previous</button>
                            <span id="candidateCurrentPage" class="pagination-page">Page 1</span>
                            <button id="candidateNextPageBtn" class="button secondary pagination-button">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Not logged in users are redirected to login page -->

    <script src="dashboard.js"></script>
</body>

</html>
