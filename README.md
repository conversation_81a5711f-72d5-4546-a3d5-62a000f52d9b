# TestGorilla Assessment Management System

This project provides a comprehensive interface for managing TestGorilla assessments and candidates, with authentication, filtering, and bulk operations capabilities.

Deployed at https://proxy.testgorilla.coolify.orderstack.dev/

## How It Works

1. A Node.js Express server acts as a proxy between your browser and the TestGorilla API
2. The proxy server forwards requests to TestGorilla with all the necessary headers
3. The proxy server returns the responses back to your browser
4. This bypasses CORS restrictions since the request to TestGorilla comes from the server, not the browser
5. Authentication tokens are stored in local storage for persistent login
6. The dashboard provides a clean interface for viewing and managing assessments and candidates

## Setup Instructions

1. Install dependencies:

   ```
   npm install
   ```

2. Start the proxy server:

   ```
   npm start
   ```

3. Access the login page:
   ```
   http://localhost:3000/login.html
   ```

## Files

- `server.js` - The Node.js proxy server that handles API requests to TestGorilla
- `login.html` - Login page with form for TestGorilla authentication
- `dashboard.html` - Dashboard page for managing assessments and candidates
- `package.json` - Project dependencies and scripts
- `.eslintrc.json` - ESLint configuration for code quality
- `.prettierrc.json` - Prettier configuration for consistent code formatting
- `.prettierignore` - List of files and directories to exclude from Prettier formatting

## Features

- Proxy endpoints for TestGorilla API (login, assessments, candidates)
- Clean, modern UI for login and dashboard
- Token storage in local storage with expiry time
- Session management with automatic logout on token expiry
- Detailed error handling
- Assessment listing with pagination
- Candidate management with filtering by status and stage
- Bulk select and delete functionality for candidates
- Responsive design for various screen sizes

## Candidate Management

- View candidates for each assessment by clicking "View Candidates"
- Filter candidates by status (Completed, Started, Invited, Expired)
- Filter candidates by stage (Evaluated, Invited for Interview, Rejected, Not Yet Evaluated)
- Select individual candidates using checkboxes
- Select all candidates at once using the header checkbox
- Delete selected candidates in bulk with the "Delete Selected" button
- Pagination for navigating through large candidate lists

## Code Quality

- ESLint is configured for code quality checking
- Prettier is configured for consistent code formatting
- Run `npm run format` to format all files according to the Prettier configuration
- Run `npm run format:check` to check if files are formatted correctly without making changes

## Notes

- The proxy server runs on port 3000 by default
- All requests to TestGorilla are made with headers that make them appear to come from the TestGorilla website
- The server logs request and response details for debugging purposes
- Tokens expire after 24 hours by default
- The dashboard is protected and requires authentication
