{"name": "testgorilla-proxy", "version": "1.0.0", "description": "Proxy server for TestGorilla API requests", "main": "server.js", "scripts": {"start": "node server.js", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "node-fetch": "^2.6.9"}, "devDependencies": {"eslint": "^9.25.1", "prettier": "^3.2.5", "eslint-config-prettier": "^9.1.0"}}